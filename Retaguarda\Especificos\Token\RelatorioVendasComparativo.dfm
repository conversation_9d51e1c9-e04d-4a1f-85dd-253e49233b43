object f_RelatorioVendasComparativo: Tf_RelatorioVendasComparativo
  Left = 0
  Top = 0
  ActiveControl = edtMesPrevisao
  Caption = 'Relat'#243'rio Comparativo de Vendas'
  ClientHeight = 700
  ClientWidth = 1200
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  WindowState = wsMaximized
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object pnlTop: TPanel
    Left = 0
    Top = 0
    Width = 1200
    Height = 141
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      1200
      141)
    object pnlParametros: TPanel
      Left = 8
      Top = 8
      Width = 200
      Height = 44
      BevelOuter = bvLowered
      TabOrder = 0
      object lblMesAno: TLabel
        Left = 8
        Top = 4
        Width = 135
        Height = 13
        Caption = 'M'#234's/Ano para Previs'#227'o:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object edtMesPrevisao: TEdit
        Left = 8
        Top = 20
        Width = 30
        Height = 21
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 0
        Text = '6'
      end
      object edtAnoPrevisao: TEdit
        Left = 44
        Top = 20
        Width = 50
        Height = 21
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 1
        Text = '2025'
      end
    end
    object pnlResumo: TPanel
      Left = 8
      Top = 60
      Width = 1184
      Height = 69
      Anchors = [akLeft, akTop, akRight]
      BevelOuter = bvLowered
      TabOrder = 1
      object lblVendasM1: TLabel
        Left = 2
        Top = 52
        Width = 115
        Height = 13
        Caption = 'Vendas de ABRIL/25'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblValorVendasM1: TLabel
        Left = 136
        Top = 51
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblPercVarM1: TLabel
        Left = 194
        Top = 51
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGreen
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblVendasM2: TLabel
        Left = 5
        Top = 30
        Width = 112
        Height = 13
        Caption = 'Vendas de MAIO/25'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblValorVendasM2: TLabel
        Left = 136
        Top = 28
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblPercVarM2: TLabel
        Left = 194
        Top = 27
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGreen
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblVendasAteDia: TLabel
        Left = 15
        Top = 7
        Width = 100
        Height = 13
        Caption = 'Vendas at'#233' dia 23'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblValorVendasAteDia: TLabel
        Left = 136
        Top = 5
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblPercVarAtual: TLabel
        Left = 194
        Top = 5
        Width = 8
        Height = 16
        Caption = '0'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGreen
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
    end
  end
  object pnlGrid: TPanel
    Left = 0
    Top = 141
    Width = 1200
    Height = 519
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 1
    ExplicitTop = 160
    ExplicitHeight = 500
    object dbgVendas: TDBGrid
      Left = 0
      Top = 0
      Width = 1200
      Height = 519
      Align = alClient
      DataSource = dsVendas
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect]
      ParentFont = False
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = [fsBold]
      OnDrawColumnCell = dbgVendasDrawColumnCell
    end
  end
  object pnlButtons: TPanel
    Left = 0
    Top = 660
    Width = 1200
    Height = 40
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    object btnAtualizar: TBitBtn
      Left = 8
      Top = 8
      Width = 100
      Height = 25
      Caption = '&Atualizar'
      TabOrder = 0
      OnClick = btnAtualizarClick
    end
    object btnExportar: TBitBtn
      Left = 116
      Top = 8
      Width = 100
      Height = 25
      Caption = '&Exportar'
      TabOrder = 1
      OnClick = btnExportarClick
    end
    object btnFechar: TBitBtn
      Left = 224
      Top = 8
      Width = 100
      Height = 25
      Caption = '&Fechar'
      Kind = bkClose
      NumGlyphs = 2
      TabOrder = 2
      OnClick = btnFecharClick
    end
  end
  object qryDados: TADOQuery
    Connection = DataModule1.ADOConnection1
    Parameters = <>
    Left = 1000
    Top = 200
  end
  object qryResumo: TADOQuery
    Connection = DataModule1.ADOConnection1
    Parameters = <>
    Left = 1000
    Top = 240
  end
  object cdsVendas: TClientDataSet
    Aggregates = <>
    Params = <>
    Left = 1000
    Top = 280
  end
  object dsVendas: TDataSource
    DataSet = cdsVendas
    Left = 1000
    Top = 320
  end
end
