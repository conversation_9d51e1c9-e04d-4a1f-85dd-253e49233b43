unit RelatorioVendasSimplificado;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Data.DB, Data.Win.ADODB, Vcl.Grids, 
  Vcl.DBGrids, Vcl.ExtCtrls, DateUtils, Vcl.ComCtrls;

type
  TfrmRelatorioVendasSimplificado = class(TForm)
    pnlTop: TPanel;
    lblMes: TLabel;
    edtMes: TEdit;
    lblAno: TLabel;
    edtAno: TEdit;
    btnCarregar: TButton;
    dbgVendas: TDBGrid;
    qryVendas: TADOQuery;
    dsVendas: TDataSource;
    pnlBottom: TPanel;
    btnFechar: TButton;
    procedure FormCreate(Sender: TObject);
    procedure btnCarregarClick(Sender: TObject);
    procedure btnFecharClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  frmRelatorioVendasSimplificado: TfrmRelatorioVendasSimplificado;

implementation

uses dm;

{$R *.dfm}

procedure TfrmRelatorioVendasSimplificado.FormCreate(Sender: TObject);
begin
  // Configurar conexao
  if Assigned(DataModule1) and Assigned(DataModule1.ADOConnection1) then
  begin
    qryVendas.Connection := DataModule1.ADOConnection1;
    dsVendas.DataSet := qryVendas;
    dbgVendas.DataSource := dsVendas;
    
    // Valores iniciais
    edtMes.Text := IntToStr(MonthOf(Now));
    edtAno.Text := IntToStr(YearOf(Now));
  end
  else
  begin
    ShowMessage('Erro: Conexao nao disponivel');
  end;
end;

procedure TfrmRelatorioVendasSimplificado.btnCarregarClick(Sender: TObject);
var
  Mes, Ano: Integer;
  DataInicio, DataFim: TDateTime;
  SQL: string;
begin
  try
    Mes := StrToIntDef(edtMes.Text, MonthOf(Now));
    Ano := StrToIntDef(edtAno.Text, YearOf(Now));
    
    if (Mes < 1) or (Mes > 12) then
    begin
      ShowMessage('Mes deve estar entre 1 e 12');
      Exit;
    end;
    
    DataInicio := EncodeDate(Ano, Mes, 1);
    DataFim := IncMonth(DataInicio, 1) - 1;
    
    // SQL simples para testar
    SQL := 'SELECT ' +
           '  DAY(Data) AS Dia, ' +
           '  SUM(TotalPrecoVenda) AS TotalVendas, ' +
           '  SUM(LucroBruto) AS TotalLucro, ' +
           '  CASE WHEN SUM(TotalPrecoVenda) > 0 THEN ' +
           '    (SUM(LucroBruto) / SUM(TotalPrecoVenda)) * 100 ' +
           '  ELSE 0 END AS MargemPercent ' +
           'FROM vw_Vendas_e_Devolucao_OS ' +
           'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', DataInicio) + ''' ' +
           '  AND Data <= ''' + FormatDateTime('yyyy-mm-dd', DataFim) + ''' ' +
           'GROUP BY DAY(Data) ' +
           'ORDER BY DAY(Data)';
    
    qryVendas.Close;
    qryVendas.SQL.Text := SQL;
    qryVendas.Open;
    
    Caption := 'Relatorio Vendas - ' + FormatDateTime('mm/yyyy', DataInicio) + 
               ' (' + IntToStr(qryVendas.RecordCount) + ' dias com vendas)';
    
  except
    on E: Exception do
      ShowMessage('Erro ao carregar dados: ' + E.Message);
  end;
end;

procedure TfrmRelatorioVendasSimplificado.btnFecharClick(Sender: TObject);
begin
  Close;
end;

end.
