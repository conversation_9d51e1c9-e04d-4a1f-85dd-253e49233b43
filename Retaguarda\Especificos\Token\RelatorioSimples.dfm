object frmRelatorioSimples: TfrmRelatorioSimples
  Left = 0
  Top = 0
  Caption = 'Relatorio Simples - Teste'
  ClientHeight = 500
  ClientWidth = 700
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnCreate = FormCreate
  PixelsPerInch = 96
  TextHeight = 13
  object lblTitulo: TLabel
    Left = 20
    Top = 20
    Width = 200
    Height = 16
    Caption = 'Digite uma query SQL e clique Executar'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object btnExecutar: TButton
    Left = 300
    Top = 140
    Width = 100
    Height = 30
    Caption = 'Executar'
    TabOrder = 0
    OnClick = btnExecutarClick
  end
  object dbgDados: TDBGrid
    Left = 20
    Top = 180
    Width = 660
    Height = 300
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
  end
  object memoSQL: TMemo
    Left = 20
    Top = 50
    Width = 660
    Height = 80
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Courier New'
    Font.Style = []
    ParentFont = False
    ScrollBars = ssVertical
    TabOrder = 2
  end
  object qryTeste: TADOQuery
    Parameters = <>
    Left = 600
    Top = 20
  end
  object dsTeste: TDataSource
    Left = 640
    Top = 20
  end
end
