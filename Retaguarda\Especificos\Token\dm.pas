unit dm;

interface

uses
  System.SysUtils, System.Classes, Data.DB, Data.Win.ADODB, IniFiles;

type
  TDataModule1 = class(TDataModule)
    ADOConnection1: TADOConnection;
    procedure DataModuleCreate(Sender: TObject);
  private
    { Private declarations }
    procedure ConfigurarConexao;
    function ObterCaminhoConfig: string;
  public
    { Public declarations }
    procedure ConectarBanco;
  end;

var
  DataModule1: TDataModule1;

implementation

{%CLASSGROUP 'Vcl.Controls.TControl'}

{$R *.dfm}

procedure TDataModule1.DataModuleCreate(Sender: TObject);
begin
  ConfigurarConexao;
  ConectarBanco;
end;

function TDataModule1.ObterCaminhoConfig: string;
begin
  Result := ExtractFilePath(ParamStr(0)) + 'config.ini';
end;

procedure TDataModule1.ConfigurarConexao;
var
  IniFile: TIniFile;
  Servidor, Banco: string;
  ConnectionString: string;
begin
  try
    IniFile := TIniFile.Create(ObterCaminhoConfig);
    try
      // Ler configurações do arquivo .ini
      Servidor := IniFile.ReadString('DATABASE', 'Server', '192.168.0.200\sql2014dev');
      Banco := IniFile.ReadString('DATABASE', 'Database', 'g_token');

      // Montar string de conexão ADO para SQL Server
      ConnectionString := 'Provider=SQLOLEDB.1;' +
                         'Data Source=' + Servidor + ';' +
                         'Initial Catalog=' + Banco + ';' +
                         'User ID=sa;' +
                         'Password=!Ucrania#1995;' +
                         'Persist Security Info=True;';

      // Configurar conexão
      ADOConnection1.Close;
      ADOConnection1.ConnectionString := ConnectionString;
      ADOConnection1.LoginPrompt := False;

    finally
      IniFile.Free;
    end;
  except
    on E: Exception do
    begin
      raise Exception.Create('Erro ao configurar conexão: ' + E.Message);
    end;
  end;
end;

procedure TDataModule1.ConectarBanco;
begin
  try
    if not ADOConnection1.Connected then
    begin
      ADOConnection1.Open;
    end;
  except
    on E: Exception do
    begin
      raise Exception.Create('Erro ao conectar com o banco de dados: ' + E.Message +
                           #13#10 + 'Verifique as configurações no arquivo config.ini');
    end;
  end;
end;

end.
