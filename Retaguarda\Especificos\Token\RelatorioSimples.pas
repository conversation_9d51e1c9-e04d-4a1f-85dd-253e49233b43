unit RelatorioSimples;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Data.DB, Data.Win.ADODB, Vcl.Grids, Vcl.DBGrids;

type
  TfrmRelatorioSimples = class(TForm)
    btnExecutar: TButton;
    dbgDados: TDBGrid;
    qryTeste: TADOQuery;
    dsTeste: TDataSource;
    memoSQL: TMemo;
    lblTitulo: TLabel;
    procedure btnExecutarClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  frmRelatorioSimples: TfrmRelatorioSimples;

implementation

uses dm;

{$R *.dfm}

procedure TfrmRelatorioSimples.FormCreate(Sender: TObject);
begin
  // Configurar conexao
  if Assigned(DataModule1) and Assigned(DataModule1.ADOConnection1) then
  begin
    qryTeste.Connection := DataModule1.ADOConnection1;
    dsTeste.DataSet := qryTeste;
    dbgDados.DataSource := dsTeste;
    
    // SQL de exemplo
    memoSQL.Lines.Clear;
    memoSQL.Lines.Add('SELECT TOP 10');
    memoSQL.Lines.Add('  GETDATE() as DataAtual,');
    memoSQL.Lines.Add('  ''Teste'' as Descricao,');
    memoSQL.Lines.Add('  1000.50 as Valor');
    memoSQL.Lines.Add('-- Substitua por sua query real');
  end
  else
  begin
    ShowMessage('Erro: Conexao nao disponivel');
  end;
end;

procedure TfrmRelatorioSimples.btnExecutarClick(Sender: TObject);
begin
  try
    if memoSQL.Lines.Text = '' then
    begin
      ShowMessage('Digite uma query SQL no campo acima');
      Exit;
    end;
    
    qryTeste.Close;
    qryTeste.SQL.Text := memoSQL.Lines.Text;
    qryTeste.Open;
    
    lblTitulo.Caption := 'Registros encontrados: ' + IntToStr(qryTeste.RecordCount);
    
  except
    on E: Exception do
      ShowMessage('Erro ao executar query: ' + E.Message);
  end;
end;

end.
