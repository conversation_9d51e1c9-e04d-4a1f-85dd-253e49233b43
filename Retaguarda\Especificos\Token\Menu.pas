unit Menu;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls;

type
  TForm1 = class(TForm)
    btnTestarConexao: TButton;
    btnRelatorioVendas: TButton;
    procedure btnRelatorioVendasClick(Sender: TObject);
    procedure btnTestarConexaoClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  Form1: TForm1;

implementation

uses RelatorioVendasComparativo, TesteConexao;

{$R *.dfm}

procedure TForm1.btnTestarConexaoClick(Sender: TObject);
var
  frmTeste: TfrmTesteConexao;
begin
  try
    frmTeste := TfrmTesteConexao.Create(Self);
    try
      frmTeste.ShowModal;
    finally
      frmTeste.Free;
    end;
  except
    on E: Exception do
      ShowMessage('Erro ao abrir teste: ' + E.Message);
  end;
end;

procedure TForm1.btnRelatorioVendasClick(Sender: TObject);
var
  frmRelatorio: Tf_RelatorioVendasComparativo;
begin
  try
    frmRelatorio := Tf_RelatorioVendasComparativo.Create(Self);
    try
      frmRelatorio.ShowModal;
    finally
      frmRelatorio.Free;
    end;
  except
    on E: Exception do
      ShowMessage('Erro ao abrir relatório: ' + E.Message);
  end;
end;

end.
