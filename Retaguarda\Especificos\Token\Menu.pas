unit Menu;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls;

type
  TForm1 = class(TForm)
    btnRelatorioSimples: TButton;
    btnRelatorioVendas: TButton;
    procedure btnRelatorioVendasClick(Sender: TObject);
    procedure btnRelatorioSimplesClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  Form1: TForm1;

implementation

uses RelatorioVendasComparativo, RelatorioSimples;

{$R *.dfm}

procedure TForm1.btnRelatorioSimplesClick(Sender: TObject);
var
  frmSimples: TfrmRelatorioSimples;
begin
  try
    frmSimples := TfrmRelatorioSimples.Create(Self);
    try
      frmSimples.ShowModal;
    finally
      frmSimples.Free;
    end;
  except
    on E: Exception do
      ShowMessage('Erro ao abrir relatorio simples: ' + E.Message);
  end;
end;

procedure TForm1.btnRelatorioVendasClick(Sender: TObject);
var
  frmRelatorio: Tf_RelatorioVendasComparativo;
begin
  try
    frmRelatorio := Tf_RelatorioVendasComparativo.Create(Self);
    try
      frmRelatorio.ShowModal;
    finally
      frmRelatorio.Free;
    end;
  except
    on E: Exception do
      ShowMessage('Erro ao abrir relatorio: ' + E.Message);
  end;
end;

end.
