unit TesteConexao;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Data.DB, Data.Win.ADODB;

type
  TfrmTesteConexao = class(TForm)
    btnTestarConexao: TButton;
    memoLog: TMemo;
    procedure btnTestarConexaoClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  frmTesteConexao: TfrmTesteConexao;

implementation

uses dm;

{$R *.dfm}

procedure TfrmTesteConexao.FormCreate(Sender: TObject);
begin
  memoLog.Lines.Clear;
  memoLog.Lines.Add('=== TESTE DE CONEXÃO ===');
  memoLog.Lines.Add('');
end;

procedure TfrmTesteConexao.btnTestarConexaoClick(Sender: TObject);
var
  qryTeste: TADOQuery;
begin
  memoLog.Lines.Add('Iniciando teste de conexão...');
  
  try
    // Verificar se o DataModule existe
    if not Assigned(DataModule1) then
    begin
      memoLog.Lines.Add('ERRO: DataModule1 não foi criado!');
      Exit;
    end;
    
    memoLog.Lines.Add('DataModule1 OK');
    
    // Verificar se a conexão existe
    if not Assigned(DataModule1.ADOConnection1) then
    begin
      memoLog.Lines.Add('ERRO: ADOConnection1 não existe!');
      Exit;
    end;
    
    memoLog.Lines.Add('ADOConnection1 OK');
    
    // Verificar se está conectado
    if not DataModule1.ADOConnection1.Connected then
    begin
      memoLog.Lines.Add('Conexão não está ativa. Tentando conectar...');
      DataModule1.ConectarBanco;
    end;
    
    if DataModule1.ADOConnection1.Connected then
    begin
      memoLog.Lines.Add('SUCESSO: Conectado ao banco de dados!');
      memoLog.Lines.Add('String de conexão: ' + DataModule1.ADOConnection1.ConnectionString);
      
      // Testar uma query simples
      qryTeste := TADOQuery.Create(Self);
      try
        qryTeste.Connection := DataModule1.ADOConnection1;
        qryTeste.SQL.Text := 'SELECT GETDATE() as DataAtual';
        qryTeste.Open;
        
        memoLog.Lines.Add('Teste de query executado com sucesso!');
        memoLog.Lines.Add('Data do servidor: ' + qryTeste.FieldByName('DataAtual').AsString);
        
      finally
        qryTeste.Free;
      end;
    end
    else
    begin
      memoLog.Lines.Add('ERRO: Não foi possível conectar ao banco!');
    end;
    
  except
    on E: Exception do
    begin
      memoLog.Lines.Add('ERRO: ' + E.Message);
    end;
  end;
  
  memoLog.Lines.Add('');
  memoLog.Lines.Add('Teste finalizado.');
end;

end.
