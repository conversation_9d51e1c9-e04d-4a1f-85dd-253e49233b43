﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{9CC0CBC6-06AB-41BF-A964-613FF86F1DAC}</ProjectGuid>
        <ProjectVersion>15.4</ProjectVersion>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>Projeto.dpr</MainSource>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>1</TargetedPlatforms>
        <AppType>Application</AppType>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>Projeto</SanitizedProjectName>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_E>false</DCC_E>
        <DCC_N>false</DCC_N>
        <DCC_S>false</DCC_S>
        <DCC_F>false</DCC_F>
        <DCC_K>false</DCC_K>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_UsePackage>ACBr_NFeDanfeFPDF;ACBr_BoletoFR;lmdrtdialog;lmdrttoolsdb;RtmRxCtl200;ACBr_BoletoRL;FireDACPgDriver;ACBr_GNRE;ACBr_NFeDanfeFR;DBXInterBaseDriver;DataSnapServer;DataSnapCommon;ACBre_Social;ACBr_BlocoX;ACBr_synapse;Rave110VCL;DbxCommonDriver;lmdrtinspector;vclimg;ACBr_NF3e;dbxcds;ACBr_Comum;DatasnapConnectorsFreePascal;ACBr_GTIN;DelPrintDXE6;vcldb;ACBr_OpenDelivery;lmdrtweb;lmdrtprint;ACBr_MDFeDamdfeRL;lmdrtchartdb;ACBr_SATExtratoESCPOS;ACBr_PagFor;lmdrtrtfdb;ACBr_GNREGuiaRL;CustomIPTransport;dsnap;ACBr_MDFe;CloudService;FmxTeeUI;FireDACIBDriver;lmdrtrtf;ACBr_SAT;ACBr_NFSeXDanfseFPDF;lmdrtide;ACBr_CTeDacteFR;lmdrtrtlx;dsnapxml;ACBr_Ponto;FireDACDb2Driver;Lck;ACBR_DeSTDA;ACBr_BoletoFPDF;lmdrtshell;ACBr_SEF2;bindcompfmx;frx20;ACBr_PAF;vcldbx;FireDACODBCDriver;RESTBackendComponents;dbrtl;lmdrtstorage;FireDACCommon;bindcomp;inetdb;IndyCore200;lmdrtgrid;ACBr_PAFNFCe;FideliMx;ACBr_NFe;DBXOdbcDriver;vclFireDAC;ACBr_NF3eDANF3eRL;xmlrtl;ibxpress;FireDACCommonDriver;bindengine;vclactnband;soaprtl;FMXTee;ACBr_CTeDacteRL;bindcompvcl;ACBr_BPe;vclie;QRWRunDXE6w64;ACBr_TEFD;ACBr_Integrador;FireDACMSSQLDriver;DBXInformixDriver;Intraweb;ACBr_SATExtratoRL;DataSnapServerMidas;dsnapcon;DBXFirebirdDriver;ACBr_NFSeXDanfseRL;inet;FireDACMySQLDriver;soapmidas;vclx;ACBr_SPEDImportar;ACBr_SATExtratoFR;DBXSybaseASADriver;RESTComponents;lmdrtsearchdb;dbexpress;EurekaLogCore;ACBr_Convenio115;ACBr_DCe;ACBr_OFX;lmdrttools;RtmRxDB200;IndyProtocols200;FireDACSqliteDriver;lmdrtscript;ACBr_LCDPR;FireDACDSDriver;ZComponent;DBXSqliteDriver;AbbreviaVCL;lmdrttxtdb;lmdrtchart;ACBr_EDI;ACBr_DCeDACERL;fmx;lmdrttxt;ACBr_Diversos;TeeDB;tethering;ACBrBaaS;ACBr_CIOT;inetdbbde;vclib;DataSnapClient;ACBr_NFeDanfeESCPOS;DataSnapProviderClient;DBXSybaseASEDriver;ACBr_MDFeDamdfeFR;IndySystem200;MetropolisUILiveTile;ACBr_ONE;ACBr_NFSeDanfseFR;ContraSenha;ACBr_BPeDabpeESCPOS;vcldsnap;lmdrtsys;lmdrteldb;fmxFireDAC;DBXDb2Driver;frce;DBXOracleDriver;ACBr_Serial;vclribbon;ACBr_SPED;ACBr_DebitoAutomatico;inetwinsockets;fmxase;vcl;ACBr_NFSeDanfseRL;lmdrtelpro;DBXMSSQLDriver;CodeSiteExpressPkg;ACBr_NF3eDANF3eESCPOS;ACBr_Boleto;DataSnapFireDAC;FireDACDBXDriver;ACBr_Sintegra;soapserver;inetdbxpress;lmdrtdocking;RtmRxBDE200;frxTee20;ACBr_NFSe;ACBr_NFCom;FireDACInfxDriver;ACBr_LFD;ACBr_SATExtratoFPDF;ACBr_PCNComum;adortl;frxDB20;ACBr_TCP;lmdrtcore;FireDACASADriver;lmdrtthemes;ACBr_NFSeX;ZDbc;rtl;DbxClientDriver;ZPlain;lmdrtbar;ACBr_MTER;Tee;ACBr_GNREGuiaFR;ACBr_CTe;ACBr_ANe;ACBr_NFCeECFVirtual;DataSnapNativeClient;svnui;lmdrtdesign;ACBr_NFeDanfeRL;lmdrtsearch;DBXMySQLDriver;bindcompdbx;TeeUI;FireDACADSDriver;vcltouch;ZCore;lmdrtelcore;ACBr_Reinf;ACBr_ADRCST;ACBr_SATWS;VclSmp;FireDAC;VCLRESTComponents;ACBr_NFSeXDANFSeFR;ACBr_PIXCD;DataSnapConnectors;lmdrtplugin;ACBr_SATECFVirtual;Abbrevia;fmxobj;ZParseSql;lmdrtsyntax;fs20;lmdrtfx;svn;FireDACOracleDriver;fmxdae;bdertl;lmdrtl;FireDACMSAccDriver;DataSnapIndy10ServerTransport;lmdrtscriptimp;frxe20;$(DCC_UsePackage)</DCC_UsePackage>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=</VerInfo_Keys>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <DCC_UsePackage>lmdrtdialog;lmdrttoolsdb;FireDACPgDriver;DBXInterBaseDriver;DataSnapServer;DataSnapCommon;DbxCommonDriver;lmdrtinspector;vclimg;dbxcds;DatasnapConnectorsFreePascal;vcldb;lmdrtweb;lmdrtprint;lmdrtchartdb;lmdrtrtfdb;CustomIPTransport;dsnap;CloudService;FireDACIBDriver;lmdrtrtf;lmdrtide;lmdrtrtlx;dsnapxml;FireDACDb2Driver;lmdrtshell;bindcompfmx;FireDACODBCDriver;RESTBackendComponents;dbrtl;lmdrtstorage;FireDACCommon;bindcomp;inetdb;IndyCore200;lmdrtgrid;DBXOdbcDriver;vclFireDAC;xmlrtl;ibxpress;FireDACCommonDriver;bindengine;vclactnband;soaprtl;bindcompvcl;vclie;QRWRunDXE6w64;FireDACMSSQLDriver;DBXInformixDriver;Intraweb;DataSnapServerMidas;dsnapcon;DBXFirebirdDriver;inet;FireDACMySQLDriver;soapmidas;vclx;DBXSybaseASADriver;RESTComponents;lmdrtsearchdb;dbexpress;lmdrttools;IndyProtocols200;FireDACSqliteDriver;lmdrtscript;FireDACDSDriver;ZComponent;DBXSqliteDriver;AbbreviaVCL;lmdrttxtdb;lmdrtchart;fmx;lmdrttxt;tethering;vclib;DataSnapClient;DataSnapProviderClient;DBXSybaseASEDriver;IndySystem200;MetropolisUILiveTile;vcldsnap;lmdrtsys;lmdrteldb;fmxFireDAC;DBXDb2Driver;DBXOracleDriver;vclribbon;inetwinsockets;fmxase;vcl;lmdrtelpro;DBXMSSQLDriver;DataSnapFireDAC;FireDACDBXDriver;soapserver;inetdbxpress;lmdrtdocking;FireDACInfxDriver;adortl;lmdrtcore;FireDACASADriver;lmdrtthemes;ZDbc;rtl;DbxClientDriver;ZPlain;lmdrtbar;DataSnapNativeClient;lmdrtdesign;lmdrtsearch;DBXMySQLDriver;bindcompdbx;FireDACADSDriver;vcltouch;ZCore;lmdrtelcore;VclSmp;FireDAC;VCLRESTComponents;DataSnapConnectors;lmdrtplugin;Abbrevia;fmxobj;ZParseSql;lmdrtsyntax;lmdrtfx;FireDACOracleDriver;fmxdae;lmdrtl;FireDACMSAccDriver;DataSnapIndy10ServerTransport;lmdrtscriptimp;$(DCC_UsePackage)</DCC_UsePackage>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_DebugInformation>0</DCC_DebugInformation>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="Menu.pas">
            <Form>Form1</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="dm.pas">
            <Form>DataModule1</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="RelatorioSimples.pas">
            <Form>frmRelatorioSimples</Form>
        </DCCReference>
        <DCCReference Include="RelatorioVendasComparativo.pas">
            <Form>f_RelatorioVendasComparativo</Form>
        </DCCReference>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">Projeto.dpr</Source>
                </Source>
            </Delphi.Personality>
            <Deployment/>
            <Platforms>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>

<!-- EurekaLog First Line
[Exception Log]
EurekaLog Version=7007
Activate=0
DeleteMapAfterCompile=0
Encrypt Password=""
idEurekaLog=1
idEurekaLogDetailed=1
idMSClassic=1
idStepsToReproduce=1
ProjectID="{D6A86592-828F-40A7-8282-525FA05988E4}"
EurekaLog Last Line -->
