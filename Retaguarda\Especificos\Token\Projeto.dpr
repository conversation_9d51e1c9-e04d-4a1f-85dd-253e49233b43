program Projeto;

uses
  Vcl.Forms,
  <PERSON>u in 'Menu.pas' {Form1},
  dm in 'dm.pas' {DataModule1: TDataModule},
  TesteConexao in 'TesteConexao.pas' {frmTesteConexao},
  RelatorioSimples in 'RelatorioSimples.pas' {frmRelatorioSimples},
  RelatorioVendasComparativo in 'RelatorioVendasComparativo.pas' {f_RelatorioVendasComparativo};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TForm1, Form1);
  Application.CreateForm(TDataModule1, DataModule1);
  Application.Run;
end.
