program Projeto;

uses
  Vcl.Forms,
  Menu in 'Menu.pas' {Form1},
  dm in 'dm.pas' {DataModule1: TDataModule},
  RelatorioSimples in 'RelatorioSimples.pas' {frmRelatorioSimples},
  RelatorioVendasComparativo in 'RelatorioVendasComparativo.pas' {f_RelatorioVendasComparativo};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TForm1, Form1);
  Application.CreateForm(TDataModule1, DataModule1);
  Application.Run;
end.
