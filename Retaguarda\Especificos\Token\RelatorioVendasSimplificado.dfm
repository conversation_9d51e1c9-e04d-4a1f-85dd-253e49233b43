object frmRelatorioVendasSimplificado: TfrmRelatorioVendasSimplificado
  Left = 0
  Top = 0
  Caption = 'Relatorio Vendas Simplificado'
  ClientHeight = 500
  ClientWidth = 800
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnCreate = FormCreate
  PixelsPerInch = 96
  TextHeight = 13
  object pnlTop: TPanel
    Left = 0
    Top = 0
    Width = 800
    Height = 60
    Align = alTop
    TabOrder = 0
    object lblMes: TLabel
      Left = 20
      Top = 20
      Width = 22
      Height = 13
      Caption = 'Mes:'
    end
    object lblAno: TLabel
      Left = 120
      Top = 20
      Width = 23
      Height = 13
      Caption = 'Ano:'
    end
    object edtMes: TEdit
      Left = 50
      Top = 17
      Width = 50
      Height = 21
      TabOrder = 0
    end
    object edtAno: TEdit
      Left = 150
      Top = 17
      Width = 60
      Height = 21
      TabOrder = 1
    end
    object btnCarregar: TButton
      Left = 230
      Top = 15
      Width = 80
      Height = 25
      Caption = 'Carregar'
      TabOrder = 2
      OnClick = btnCarregarClick
    end
  end
  object dbgVendas: TDBGrid
    Left = 0
    Top = 60
    Width = 800
    Height = 400
    Align = alClient
    DataSource = dsVendas
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
  end
  object pnlBottom: TPanel
    Left = 0
    Top = 460
    Width = 800
    Height = 40
    Align = alBottom
    TabOrder = 2
    object btnFechar: TButton
      Left = 350
      Top = 8
      Width = 100
      Height = 25
      Caption = 'Fechar'
      TabOrder = 0
      OnClick = btnFecharClick
    end
  end
  object qryVendas: TADOQuery
    Parameters = <>
    Left = 720
    Top = 20
  end
  object dsVendas: TDataSource
    Left = 760
    Top = 20
  end
end
