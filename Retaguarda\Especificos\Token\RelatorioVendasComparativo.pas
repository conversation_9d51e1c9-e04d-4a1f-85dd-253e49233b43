﻿unit RelatorioVendasComparativo;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, Grids, DBGrids, DB, ComCtrls, Buttons,
  Mask, DBCtrls, DateUtils, Math, DBClient, Data.Win.ADODB, dm;

type
  Tf_RelatorioVendasComparativo = class(TForm)
    pnlTop: TPanel;
    edtMesPrevisao: TEdit;
    edtAnoPrevisao: TEdit;
    pnlResumo: TPanel;
    lblVendasM2: TLabel;
    lblVendasM1: TLabel;
    lblValorVendasM2: TLabel;
    lblValorVendasM1: TLabel;
    lblPercVarM2: TLabel;
    lblPercVarM1: TLabel;
    lblVendasAteDia: TLabel;
    lblValorVendasAteDia: TLabel;
    lblPercVarAtual: TLabel;
    pnlGrid: TPanel;
    dbgVendas: TDBGrid;
    pnlButtons: TPanel;
    btnAtualizar: TBitBtn;
    btnExportar: TBitBtn;
    btnFechar: TBitBtn;
    qryDados: TADOQuery;
    qryResumo: TADOQuery;
    cdsVendas: TClientDataSet;
    dsVendas: TDataSource;
    procedure FormCreate(Sender: TObject);
    procedure btnAtualizarClick(Sender: TObject);
    procedure btnFecharClick(Sender: TObject);
    procedure btnExportarClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure dbgVendasDrawColumnCell(Sender: TObject; const Rect: TRect;
      DataCol: Integer; Column: TColumn; State: TGridDrawState);
  private
    FMesBase: Integer;
    FAnoBase: Integer;
    FCenario: Integer;
    procedure ConfigurarClientDataSet;
    procedure ConfigurarDBGrid;
    procedure CarregarDados;
//    procedure CarregarResumo;
//    procedure CalcularPrevisao;
    procedure CalcularTotaisRealizados;
    procedure ExportarParaExcel;
  public
    { Public declarations }
  end;

var
  f_RelatorioVendasComparativo: Tf_RelatorioVendasComparativo;

implementation

uses ComObj;

{$R *.dfm}

procedure Tf_RelatorioVendasComparativo.FormCreate(Sender: TObject);
begin
  try
    // Configurar conexões dos queries
    if Assigned(DataModule1) and Assigned(DataModule1.ADOConnection1) then
    begin
      qryDados.Connection := DataModule1.ADOConnection1;
      qryResumo.Connection := DataModule1.ADOConnection1;
    end
    else
    begin
      ShowMessage('Erro: Conexao com banco de dados nao disponivel');
      Exit;
    end;

    // Configurações iniciais
    FMesBase := MonthOf(Now);
    FAnoBase := YearOf(Now);
    FCenario := 2;

    edtMesPrevisao.Text := IntToStr(FMesBase);
    edtAnoPrevisao.Text := IntToStr(FAnoBase);

    ConfigurarClientDataSet;
    ConfigurarDBGrid;

  except
    on E: Exception do
    begin
      ShowMessage('Erro ao inicializar formulario: ' + E.Message);
    end;
  end;
end;

procedure Tf_RelatorioVendasComparativo.FormShow(Sender: TObject);
begin
  ConfigurarDBGrid; // Configurar cabeçalhos com as datas corretas
  CarregarDados;
  CalcularTotaisRealizados;
end;

procedure Tf_RelatorioVendasComparativo.ConfigurarClientDataSet;
var
  i: Integer;
begin
  try
    // Criar estrutura do ClientDataSet
    if cdsVendas.Active then
      cdsVendas.Close;

    cdsVendas.FieldDefs.Clear;

    // Adicionar campos
    cdsVendas.FieldDefs.Add('Status', ftString, 1); // Campo para controlar o ícone
    cdsVendas.FieldDefs.Add('Dia', ftInteger);
    cdsVendas.FieldDefs.Add('VendasMes1', ftCurrency);
    cdsVendas.FieldDefs.Add('LucroMes1', ftCurrency);
    cdsVendas.FieldDefs.Add('MargemMes1', ftFloat);
    cdsVendas.FieldDefs.Add('VendasMes2', ftCurrency);
    cdsVendas.FieldDefs.Add('LucroMes2', ftCurrency);
    cdsVendas.FieldDefs.Add('MargemMes2', ftFloat);
    cdsVendas.FieldDefs.Add('VendasMes3', ftCurrency);
    cdsVendas.FieldDefs.Add('LucroMes3', ftCurrency);
    cdsVendas.FieldDefs.Add('MargemMes3', ftFloat);

    // Criar dataset
    cdsVendas.CreateDataSet;

  // Preencher com os 31 dias
  for i := 1 to 31 do
  begin
    cdsVendas.Append;

    // Definir status do dia baseado na data atual
    if (FMesBase = MonthOf(Now)) and (FAnoBase = YearOf(Now)) then
    begin
      if i < DayOf(Now) then
        cdsVendas.FieldByName('Status').AsString := 'P' // Passado (bolinha escura)
      else if i = DayOf(Now) then
        cdsVendas.FieldByName('Status').AsString := 'A' // Atual (bolinha cortada)
      else
        cdsVendas.FieldByName('Status').AsString := 'F'; // Futuro (bolinha clara)
    end
    else if (EncodeDate(FAnoBase, FMesBase, 1) < EncodeDate(YearOf(Now), MonthOf(Now), 1)) then
      cdsVendas.FieldByName('Status').AsString := 'P' // Mês passado - todos escuros
    else
      cdsVendas.FieldByName('Status').AsString := 'F'; // Mês futuro - todos claros

    cdsVendas.FieldByName('Dia').AsInteger := i;
    cdsVendas.FieldByName('VendasMes1').AsFloat := 0;
    cdsVendas.FieldByName('LucroMes1').AsFloat := 0;
    cdsVendas.FieldByName('MargemMes1').AsFloat := 0;
    cdsVendas.FieldByName('VendasMes2').AsFloat := 0;
    cdsVendas.FieldByName('LucroMes2').AsFloat := 0;
    cdsVendas.FieldByName('MargemMes2').AsFloat := 0;
    cdsVendas.FieldByName('VendasMes3').AsFloat := 0;
    cdsVendas.FieldByName('LucroMes3').AsFloat := 0;
    cdsVendas.FieldByName('MargemMes3').AsFloat := 0;
    cdsVendas.Post;
  end;

    cdsVendas.First;

  except
    on E: Exception do
    begin
      ShowMessage('Erro ao configurar ClientDataSet: ' + E.Message);
      raise;
    end;
  end;
end;

procedure Tf_RelatorioVendasComparativo.ConfigurarDBGrid;
var
  DataMes1, DataMes2, DataMes3: TDateTime;
  NomeMes1, NomeMes2, NomeMes3: string;
begin
  // Calcular as datas dos três meses para os cabeçalhos
  DataMes3 := EncodeDate(FAnoBase, FMesBase, 1); // Mês atual
  DataMes2 := IncMonth(DataMes3, -1); // Mês anterior
  DataMes1 := IncMonth(DataMes3, -2); // Dois meses atrás

  // Formatar nomes dos meses para cabeçalhos
  NomeMes1 := FormatDateTime('mm/yyyy', DataMes1);
  NomeMes2 := FormatDateTime('mm/yyyy', DataMes2);
  NomeMes3 := FormatDateTime('mm/yyyy', DataMes3);

  // Configurar DataSource
  dsVendas.DataSet := cdsVendas;
  dbgVendas.DataSource := dsVendas;

  // Configurar colunas do DBGrid
  dbgVendas.Columns.Clear;

  // Coluna Status (ícone)
  with dbgVendas.Columns.Add do
  begin
    FieldName := 'Status';
    Title.Caption := '';
    Width := 25;
    Title.Alignment := taCenter;
    Alignment := taCenter;
  end;

  // Coluna Dia
  with dbgVendas.Columns.Add do
  begin
    FieldName := 'Dia';
    Title.Caption := 'Dia';
    Width := 40;
    Title.Alignment := taCenter;
    Alignment := taCenter;
  end;

  // Colunas do primeiro mês
  with dbgVendas.Columns.Add do
  begin
    FieldName := 'VendasMes1';
    Title.Caption := NomeMes1 + #13#10 + 'Vendas';
    Width := 120;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'LucroMes1';
    Title.Caption := 'Lucro';
    Width := 80;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'MargemMes1';
    Title.Caption := 'Margem';
    Width := 60;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  // Colunas do segundo mês
  with dbgVendas.Columns.Add do
  begin
    FieldName := 'VendasMes2';
    Title.Caption := NomeMes2 + #13#10 + 'Vendas';
    Width := 120;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'LucroMes2';
    Title.Caption := 'Lucro';
    Width := 80;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'MargemMes2';
    Title.Caption := 'Margem';
    Width := 60;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  // Colunas do terceiro mês
  with dbgVendas.Columns.Add do
  begin
    FieldName := 'VendasMes3';
    Title.Caption := NomeMes3 + #13#10 + 'Vendas';
    Width := 120;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'LucroMes3';
    Title.Caption := 'Lucro';
    Width := 80;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;

  with dbgVendas.Columns.Add do
  begin
    FieldName := 'MargemMes3';
    Title.Caption := 'Margem';
    Width := 60;
    Title.Alignment := taRightJustify;
    Alignment := taRightJustify;
  end;
end;

procedure Tf_RelatorioVendasComparativo.CarregarDados;
var
  SQL: string;
  Mes1, Mes2, Mes3: string;
  NomeMes1, NomeMes2, NomeMes3: string;
  i, Dia: Integer;
  Vendas, Lucro, Margem: Double;
  DataMes1, DataMes2, DataMes3: TDateTime;
  // Variáveis para soma acumulativa
  AcumVendasMes1, AcumLucroMes1: Double;
  AcumVendasMes2, AcumLucroMes2: Double;
  AcumVendasMes3, AcumLucroMes3: Double;
begin
  try
    FMesBase := StrToIntDef(edtMesPrevisao.Text, MonthOf(Now));
    FAnoBase := StrToIntDef(edtAnoPrevisao.Text, YearOf(Now));

    // Calcular as datas dos três meses
    DataMes3 := EncodeDate(FAnoBase, FMesBase, 1); // Mês atual
    DataMes2 := IncMonth(DataMes3, -1); // Mês anterior
    DataMes1 := IncMonth(DataMes3, -2); // Dois meses atrás

    // Formatar strings dos meses para comparação
    Mes3 := FormatDateTime('yyyy-mm', DataMes3);
    Mes2 := FormatDateTime('yyyy-mm', DataMes2);
    Mes1 := FormatDateTime('yyyy-mm', DataMes1);

    // Nomes dos meses para cabeçalhos
    NomeMes1 := FormatDateTime('mm/yyyy', DataMes1);
    NomeMes2 := FormatDateTime('mm/yyyy', DataMes2);
    NomeMes3 := FormatDateTime('mm/yyyy', DataMes3);

    // Adicionar linha com os nomes dos meses (se necessário, ajustar o grid)

    // Montar SQL compatível com SQL Server e MySQL
    SQL := 'SELECT ' +
           '  DAY(Data) AS Dia, ' +
           '  YEAR(Data) AS Ano, ' +
           '  MONTH(Data) AS Mes, ' +
           '  SUM(TotalPrecoVenda) AS TotalVendas, ' +
           '  SUM(LucroBruto) AS TotalLucro ' +
           'FROM vw_Vendas_e_Devolucao_OS ' +
           'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', DataMes1) + ''' ' +
           '  AND Data < ''' + FormatDateTime('yyyy-mm-dd', IncMonth(DataMes3, 1)) + ''' ' +
           'GROUP BY DAY(Data), YEAR(Data), MONTH(Data) ' +
           'ORDER BY YEAR(Data), MONTH(Data), DAY(Data)';

    qryDados.Close;
    qryDados.SQL.Text := SQL;
    qryDados.Open;

    // Inicializar acumuladores
    AcumVendasMes1 := 0;
    AcumLucroMes1 := 0;
    AcumVendasMes2 := 0;
    AcumLucroMes2 := 0;
    AcumVendasMes3 := 0;
    AcumLucroMes3 := 0;

    // Limpar dados do ClientDataSet
    cdsVendas.First;
    while not cdsVendas.Eof do
    begin
      cdsVendas.Edit;
      cdsVendas.FieldByName('VendasMes1').AsFloat := 0;
      cdsVendas.FieldByName('LucroMes1').AsFloat := 0;
      cdsVendas.FieldByName('MargemMes1').AsFloat := 0;
      cdsVendas.FieldByName('VendasMes2').AsFloat := 0;
      cdsVendas.FieldByName('LucroMes2').AsFloat := 0;
      cdsVendas.FieldByName('MargemMes2').AsFloat := 0;
      cdsVendas.FieldByName('VendasMes3').AsFloat := 0;
      cdsVendas.FieldByName('LucroMes3').AsFloat := 0;
      cdsVendas.FieldByName('MargemMes3').AsFloat := 0;
      cdsVendas.Post;
      cdsVendas.Next;
    end;

    // Preencher dados
    while not qryDados.Eof do
    begin
      Dia := qryDados.FieldByName('Dia').AsInteger;
      Vendas := qryDados.FieldByName('TotalVendas').AsFloat;
      Lucro := qryDados.FieldByName('TotalLucro').AsFloat;

      if (Dia >= 1) and (Dia <= 31) then
      begin
        // Verificar qual mês e acumular valores
        if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes1)) and
           (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes1)) then
        begin
          // Dois meses atrás - acumular
          AcumVendasMes1 := AcumVendasMes1 + Vendas;
          AcumLucroMes1 := AcumLucroMes1 + Lucro;
        end
        else if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes2)) and
                (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes2)) then
        begin
          // Mês anterior - acumular
          AcumVendasMes2 := AcumVendasMes2 + Vendas;
          AcumLucroMes2 := AcumLucroMes2 + Lucro;
        end
        else if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes3)) and
                (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes3)) then
        begin
          // Mês atual - acumular
          AcumVendasMes3 := AcumVendasMes3 + Vendas;
          AcumLucroMes3 := AcumLucroMes3 + Lucro;
        end;
      end;

      qryDados.Next;
    end;

    // Agora preencher o ClientDataSet com valores acumulativos
    cdsVendas.First;
    AcumVendasMes1 := 0;
    AcumLucroMes1 := 0;
    AcumVendasMes2 := 0;
    AcumLucroMes2 := 0;
    AcumVendasMes3 := 0;
    AcumLucroMes3 := 0;

    // Reabrir query para processar dia por dia em ordem
    qryDados.Close;
    qryDados.SQL.Text := SQL;
    qryDados.Open;

    while not cdsVendas.Eof do
    begin
      Dia := cdsVendas.FieldByName('Dia').AsInteger;

      // Buscar dados do dia atual nos três meses
      qryDados.First;
      while not qryDados.Eof do
      begin
        if qryDados.FieldByName('Dia').AsInteger = Dia then
        begin
          Vendas := qryDados.FieldByName('TotalVendas').AsFloat;
          Lucro := qryDados.FieldByName('TotalLucro').AsFloat;

          // Verificar qual mês e acumular
          if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes1)) and
             (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes1)) then
          begin
            // Dois meses atrás - acumular
            AcumVendasMes1 := AcumVendasMes1 + Vendas;
            AcumLucroMes1 := AcumLucroMes1 + Lucro;
          end
          else if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes2)) and
                  (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes2)) then
          begin
            // Mês anterior - acumular
            AcumVendasMes2 := AcumVendasMes2 + Vendas;
            AcumLucroMes2 := AcumLucroMes2 + Lucro;
          end
          else if (qryDados.FieldByName('Ano').AsInteger = YearOf(DataMes3)) and
                  (qryDados.FieldByName('Mes').AsInteger = MonthOf(DataMes3)) then
          begin
            // Mês atual - acumular
            AcumVendasMes3 := AcumVendasMes3 + Vendas;
            AcumLucroMes3 := AcumLucroMes3 + Lucro;
          end;
        end;
        qryDados.Next;
      end;

      // Atualizar o registro com valores acumulados
      cdsVendas.Edit;

      // Mês 1 (dois meses atrás)
      cdsVendas.FieldByName('VendasMes1').AsFloat := AcumVendasMes1;
      cdsVendas.FieldByName('LucroMes1').AsFloat := AcumLucroMes1;
      if AcumVendasMes1 > 0 then
        cdsVendas.FieldByName('MargemMes1').AsFloat := (AcumLucroMes1 / AcumVendasMes1) * 100
      else
        cdsVendas.FieldByName('MargemMes1').AsFloat := 0;

      // Mês 2 (mês anterior)
      cdsVendas.FieldByName('VendasMes2').AsFloat := AcumVendasMes2;
      cdsVendas.FieldByName('LucroMes2').AsFloat := AcumLucroMes2;
      if AcumVendasMes2 > 0 then
        cdsVendas.FieldByName('MargemMes2').AsFloat := (AcumLucroMes2 / AcumVendasMes2) * 100
      else
        cdsVendas.FieldByName('MargemMes2').AsFloat := 0;

      // Mês 3 (mês atual)
      cdsVendas.FieldByName('VendasMes3').AsFloat := AcumVendasMes3;
      cdsVendas.FieldByName('LucroMes3').AsFloat := AcumLucroMes3;
      if AcumVendasMes3 > 0 then
        cdsVendas.FieldByName('MargemMes3').AsFloat := (AcumLucroMes3 / AcumVendasMes3) * 100
      else
        cdsVendas.FieldByName('MargemMes3').AsFloat := 0;

      cdsVendas.Post;
      cdsVendas.Next;
    end;

    cdsVendas.First;

  except
    on E: Exception do
      ShowMessage('Erro ao carregar dados: ' + E.Message);
  end;
end;

//procedure Tf_RelatorioVendasComparativo.CarregarResumo;
//var
//  SQL: string;
//  VendasMes1, VendasMes2, VendasMes3: Double;
//  VendasAteDiaAtual: Double;
//  DataMes1, DataMes2, DataMes3: TDateTime;
//  NomeMes1, NomeMes2, NomeMes3: string;
//  DiaAtual: Integer;
//begin
//  try
//    // Calcular as datas dos três meses
//    DataMes3 := EncodeDate(FAnoBase, FMesBase, 1); // Mês atual
//    DataMes2 := IncMonth(DataMes3, -1); // Mês anterior
//    DataMes1 := IncMonth(DataMes3, -2); // Dois meses atrás
//
//    // Nomes dos meses para os labels
//    NomeMes1 := UpperCase(FormatDateTime('mmmm', DataMes1)) + '/' + FormatDateTime('yy', DataMes1);
//    NomeMes2 := UpperCase(FormatDateTime('mmmm', DataMes2)) + '/' + FormatDateTime('yy', DataMes2);
//    NomeMes3 := UpperCase(FormatDateTime('mmmm', DataMes3)) + '/' + FormatDateTime('yy', DataMes3);
//
//    // Atualizar labels dos nomes dos meses
//    lblVendasM1.Caption := 'Vendas de ' + NomeMes1;
//    lblVendasM2.Caption := 'Vendas de ' + NomeMes2;
//    lblVendasAteDia.Caption := 'Vendas mês atual'+NomeMes3;
//    //    lblPrevisaoJunho.Caption := 'Previsão para ' + NomeMes3;
//
//
//    // SQL para buscar totais por mês
//    SQL := 'SELECT ' +
//           '  YEAR(Data) AS Ano, ' +
//           '  MONTH(Data) AS Mes, ' +
//           '  SUM(TotalPrecoVenda) AS TotalVendas ' +
//           'FROM vw_Vendas_e_Devolucao_OS ' +
//           'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', DataMes1) + ''' ' +
//           '  AND Data < ''' + FormatDateTime('yyyy-mm-dd', IncMonth(DataMes3, 1)) + ''' ' +
//           'GROUP BY YEAR(Data), MONTH(Data) ' +
//           'ORDER BY YEAR(Data), MONTH(Data)';
//
//    qryResumo.Close;
//    qryResumo.SQL.Text := SQL;
//    qryResumo.Open;
//
//    VendasMes1 := 0;
//    VendasMes2 := 0;
//    VendasMes3 := 0;
//
//    while not qryResumo.Eof do
//    begin
//      if (qryResumo.FieldByName('Ano').AsInteger = YearOf(DataMes1)) and
//         (qryResumo.FieldByName('Mes').AsInteger = MonthOf(DataMes1)) then
//        VendasMes1 := qryResumo.FieldByName('TotalVendas').AsFloat
//      else if (qryResumo.FieldByName('Ano').AsInteger = YearOf(DataMes2)) and
//              (qryResumo.FieldByName('Mes').AsInteger = MonthOf(DataMes2)) then
//        VendasMes2 := qryResumo.FieldByName('TotalVendas').AsFloat
//      else if (qryResumo.FieldByName('Ano').AsInteger = YearOf(DataMes3)) and
//              (qryResumo.FieldByName('Mes').AsInteger = MonthOf(DataMes3)) then
//        VendasMes3 := qryResumo.FieldByName('TotalVendas').AsFloat;
//
//      qryResumo.Next;
//    end;
//
//    // Buscar vendas até o dia atual do mês
//    DiaAtual := DayOf(Now);
//    if MonthOf(Now) = FMesBase then
//    begin
//      SQL := 'SELECT SUM(TotalPrecoVenda) AS TotalVendas ' +
//             'FROM vw_Vendas_e_Devolucao_OS ' +
//             'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', DataMes3) + ''' ' +
//             '  AND Data <= ''' + FormatDateTime('yyyy-mm-dd', EncodeDate(FAnoBase, FMesBase, DiaAtual)) + '''';
//
//      qryResumo.Close;
//      qryResumo.SQL.Text := SQL;
//      qryResumo.Open;
//
//      VendasAteDiaAtual := qryResumo.FieldByName('TotalVendas').AsFloat;
//      lblVendasAteDia.Caption := 'Vendas até dia ' + IntToStr(DiaAtual);
//      lblValorVendasAteDia.Caption := FormatFloat('0', VendasAteDiaAtual);
//    end
//    else
//    begin
//      VendasAteDiaAtual := VendasMes3;
//      lblVendasAteDia.Caption := 'Vendas do mês';
//      lblValorVendasAteDia.Caption := FormatFloat('0', VendasAteDiaAtual);
//    end;
//
//    // Atualizar labels do resumo (formato simples)
//    lblValorVendasM1.Caption := FormatFloat('0', VendasMes1);
//    lblValorVendasM2.Caption := FormatFloat('0', VendasMes2);
//    lblValorVendasAteDia.Caption := FormatFloat('0', VendasMes3);
//
//    // Calcular variações
//    if VendasMes1 > 0 then
//    begin
//      lblPercVarM1.Caption := FormatFloat('0', ((VendasMes2 - VendasMes1) / VendasMes1) * 100);
//    end
//    else
//      lblPercVarM1.Caption := '0';
//
//    if VendasMes2 > 0 then
//    begin
//      lblPercVarM2.Caption := FormatFloat('0', ((VendasMes3 - VendasMes2) / VendasMes2) * 100);
//    end
//    else
//      lblPercVarM2.Caption := '0';
//
//    // Calcular variação das vendas até o dia atual
//    if VendasMes2 > 0 then
//    begin
//      lblPercVarAtual.Caption := FormatFloat('0', ((VendasAteDiaAtual - VendasMes2) / VendasMes2) * 100);
//    end
//    else
//      lblPercVarAtual.Caption := '0';
//
//    // Calcular totais realizados
//    CalcularTotaisRealizados;
//  except
//    on E: Exception do
//      ShowMessage('Erro ao carregar resumo: ' + E.Message);
//  end;
//end;

//procedure Tf_RelatorioVendasComparativo.CalcularPrevisao;
//var
//  SQL: string;
//  VendasMesAnterior, VendasAteDiaAtual: Double;
//  DiaAtual, DiasNoMes: Integer;
//  PrevisaoCalculada: Double;
//  FatorCrescimento: Double;
//begin
//  try
//    DiaAtual := DayOf(Now);
//    DiasNoMes := DaysInMonth(EncodeDate(FAnoBase, FMesBase, 1));
//
//    // Buscar vendas do mês anterior completo
//    SQL := 'SELECT SUM(TotalPrecoVenda) AS TotalVendas ' +
//           'FROM vw_Vendas_e_Devolucao_OS ' +
//           'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', IncMonth(EncodeDate(FAnoBase, FMesBase, 1), -1)) + ''' ' +
//           '  AND Data < ''' + FormatDateTime('yyyy-mm-dd', EncodeDate(FAnoBase, FMesBase, 1)) + '''';
//
//    qryResumo.Close;
//    qryResumo.SQL.Text := SQL;
//    qryResumo.Open;
//
//    VendasMesAnterior := qryResumo.FieldByName('TotalVendas').AsFloat;
//
//    // Buscar vendas até o dia atual do mês corrente
//    SQL := 'SELECT SUM(TotalPrecoVenda) AS TotalVendas ' +
//           'FROM vw_Vendas_e_Devolucao_OS ' +
//           'WHERE Data >= ''' + FormatDateTime('yyyy-mm-dd', EncodeDate(FAnoBase, FMesBase, 1)) + ''' ' +
//           '  AND Data <= ''' + FormatDateTime('yyyy-mm-dd', Now) + '''';
//
//    qryResumo.Close;
//    qryResumo.SQL.Text := SQL;
//    qryResumo.Open;
//
//    VendasAteDiaAtual := qryResumo.FieldByName('TotalVendas').AsFloat;
//
//    // Calcular previsão baseada no cenário
//    case FCenario of
//      1: // Cenário conservador - manter ritmo atual
//        PrevisaoCalculada := (VendasAteDiaAtual / DiaAtual) * DiasNoMes;
//      2: // Cenário moderado - média entre atual e mês anterior
//        begin
//          FatorCrescimento := (VendasAteDiaAtual / DiaAtual) / (VendasMesAnterior / DaysInMonth(IncMonth(EncodeDate(FAnoBase, FMesBase, 1), -1)));
//          PrevisaoCalculada := VendasMesAnterior * FatorCrescimento;
//        end;
//      3: // Cenário otimista - crescimento baseado na tendência
//        begin
//          FatorCrescimento := 1.1; // 10% de crescimento
//          PrevisaoCalculada := (VendasAteDiaAtual / DiaAtual) * DiasNoMes * FatorCrescimento;
//        end;
//    else
//      PrevisaoCalculada := (VendasAteDiaAtual / DiaAtual) * DiasNoMes;
//    end;
//
//    // Atualizar label da previsão (formato simples)
////    lblValorPrevisaoJunho.Caption := FormatFloat('0', PrevisaoCalculada);
//
//  except
//    on E: Exception do
//      ShowMessage('Erro ao calcular previsão: ' + E.Message);
//  end;
//end;

procedure Tf_RelatorioVendasComparativo.CalcularTotaisRealizados;
var
  TotalMes1, TotalMes2, TotalMes3: Double;
  NomeMes1, NomeMes2, NomeMes3: string;
  MesAtual, AnoAtual: Integer;
begin
  try
    TotalMes1 := 0;
    TotalMes2 := 0;
    TotalMes3 := 0;

    MesAtual := StrToIntDef(edtMesPrevisao.Text, MonthOf(Now));
    AnoAtual := StrToIntDef(edtAnoPrevisao.Text, YearOf(Now));

    // Calcular nomes dos meses
    NomeMes1 := UpperCase(FormatDateTime('mmm', EncodeDate(AnoAtual, MesAtual - 2, 1))) + '/' + Copy(IntToStr(AnoAtual), 3, 2);
    NomeMes2 := UpperCase(FormatDateTime('mmm', EncodeDate(AnoAtual, MesAtual - 1, 1))) + '/' + Copy(IntToStr(AnoAtual), 3, 2);
    NomeMes3 := UpperCase(FormatDateTime('mmm', EncodeDate(AnoAtual, MesAtual, 1))) + '/' + Copy(IntToStr(AnoAtual), 3, 2);

    // Somar valores do grid
    cdsVendas.last;
    TotalMes1 := cdsVendas.FieldByName('VendasMes1').AsFloat;
    TotalMes2 := cdsVendas.FieldByName('VendasMes2').AsFloat;
    TotalMes3 := cdsVendas.FieldByName('VendasMes3').AsFloat;

    // Atualizar labels dos totais realizados
    lblVendasM1.Caption := 'Vendas de ' + NomeMes1;
    lblValorVendasM1.Caption := FormatFloat('0', TotalMes1);

    lblVendasM2.Caption := 'Vendas de ' + NomeMes2;
    lblValorVendasM2.Caption := FormatFloat('0', TotalMes2);

    lblVendasAteDia.Caption := 'Vendas de ' + NomeMes3;
    lblValorVendasAteDia.Caption := FormatFloat('0', TotalMes3);

    // Calcular variações
    if TotalMes1 > 0 then begin
      lblPercVarM1.Caption := FormatFloat('0', ((TotalMes2 - TotalMes1) / TotalMes1) * 100);
    end else
      lblPercVarM1.Caption := '0';

    if TotalMes2 > 0 then begin
      lblPercVarM2.Caption := FormatFloat('0', ((TotalMes3 - TotalMes2) / TotalMes2) * 100);
    end else
      lblPercVarM2.Caption := '0';

    lblPercVarAtual.Caption := '%';
  except
    on E: Exception do
      ShowMessage('Erro ao calcular totais realizados: ' + E.Message);
  end;
end;

procedure Tf_RelatorioVendasComparativo.dbgVendasDrawColumnCell(
  Sender: TObject; const Rect: TRect; DataCol: Integer; Column: TColumn;
  State: TGridDrawState);
var
  DiaAtual: Integer;
  Valor: Double;
  CorFundo: TColor;
  Status: string;
  CentroX, CentroY, Raio: Integer;
begin
  DiaAtual := DayOf(Now);
  CorFundo := clWhite;

  // Aplicar zebrado (linhas alternadas)
  if (dbgVendas.DataSource.DataSet.RecNo mod 2) = 0 then
    CorFundo := $00F5F5F5; // Cinza bem claro


  // Destacar linha do dia atual se estivermos no mês corrente
  if (MonthOf(Now) = FMesBase) and (YearOf(Now) = FAnoBase) and
     (cdsVendas.FieldByName('Dia').AsInteger = DiaAtual) then
  begin
    CorFundo := $0080FFFF; // Amarelo claro
  end
  // Aplicar cores baseadas na margem (como na imagem original)
  else if (Column.FieldName = 'MargemMes1') or
          (Column.FieldName = 'MargemMes2') or
          (Column.FieldName = 'MargemMes3') then
  begin
    if not Column.Field.IsNull then
    begin
      Valor := Column.Field.AsFloat;
      if Valor > 50 then
        CorFundo := $0080FF80  // Verde claro
      else if Valor > 30 then
        CorFundo := $00FF8080  // Azul claro
      else if Valor > 0 then
        CorFundo := $00C0C0C0; // Cinza claro
    end;
  end;

  // Aplicar cor de fundo sempre (mesmo quando selecionado)
  dbgVendas.Canvas.Brush.Color := CorFundo;
  dbgVendas.Canvas.FillRect(Rect);

  dbgVendas.Canvas.Font.Color := clBlack;

  // Desenhar ícone de status na primeira coluna
  if Column.FieldName = 'Status' then
  begin
    Status := cdsVendas.FieldByName('Status').AsString;
    CentroX := Rect.Left + (Rect.Right - Rect.Left) div 2;
    CentroY := Rect.Top + (Rect.Bottom - Rect.Top) div 2;
    Raio := 6;

    if Status = 'P' then // Passado - bolinha escura (preta)
    begin
      dbgVendas.Canvas.Brush.Color := clBlack;
      dbgVendas.Canvas.Pen.Color := clBlack;
      dbgVendas.Canvas.Ellipse(CentroX - Raio, CentroY - Raio, CentroX + Raio, CentroY + Raio);
    end
    else if Status = 'A' then // Atual - bolinha cortada (meio preta, meio branca)
    begin
      // Desenhar círculo branco
      dbgVendas.Canvas.Brush.Color := clWhite;
      dbgVendas.Canvas.Pen.Color := clBlack;
      dbgVendas.Canvas.Ellipse(CentroX - Raio, CentroY - Raio, CentroX + Raio, CentroY + Raio);
      // Desenhar metade preta
      dbgVendas.Canvas.Brush.Color := clBlack;
      dbgVendas.Canvas.Pie(CentroX - Raio, CentroY - Raio, CentroX + Raio, CentroY + Raio,
          CentroX, CentroY - Raio, CentroX, CentroY + Raio);
    end
    else if Status = 'F' then // Futuro - bolinha clara (branca com borda)
    begin
      dbgVendas.Canvas.Brush.Color := clWhite;
      dbgVendas.Canvas.Pen.Color := clBlack;
      dbgVendas.Canvas.Ellipse(CentroX - Raio, CentroY - Raio, CentroX + Raio, CentroY + Raio);
    end;
  end
  // Formatação simples para todos os outros campos
  else if (Column.FieldName = 'VendasMes1') or (Column.FieldName = 'VendasMes2') or
     (Column.FieldName = 'VendasMes3') or (Column.FieldName = 'LucroMes1') or
     (Column.FieldName = 'LucroMes2') or (Column.FieldName = 'LucroMes3') then
  begin
    if not Column.Field.IsNull and (Column.Field.AsFloat > 0) then
    begin
      dbgVendas.Canvas.TextOut(Rect.Right - dbgVendas.Canvas.TextWidth(FormatFloat('0', Column.Field.AsFloat)) - 5,
                               Rect.Top + 2, FormatFloat('0', Column.Field.AsFloat));
    end
    else
    begin
      dbgVendas.Canvas.TextOut(Rect.Right - dbgVendas.Canvas.TextWidth('-') - 5, Rect.Top + 2, '-');
    end;
  end
  else if (Column.FieldName = 'MargemMes1') or (Column.FieldName = 'MargemMes2') or
          (Column.FieldName = 'MargemMes3') then
  begin
    if not Column.Field.IsNull and (Column.Field.AsFloat > 0) then
    begin
      dbgVendas.Canvas.TextOut(Rect.Right - dbgVendas.Canvas.TextWidth(FormatFloat('0.0', Column.Field.AsFloat) + '%') - 5,
                               Rect.Top + 2, FormatFloat('0.0', Column.Field.AsFloat) + '%');
    end
    else
    begin
      dbgVendas.Canvas.TextOut(Rect.Right - dbgVendas.Canvas.TextWidth('-') - 5, Rect.Top + 2, '-');
    end;
  end
  else if Column.FieldName = 'Dia' then
  begin
    dbgVendas.Canvas.TextOut(Rect.Left + 2, Rect.Top + 2, Column.Field.AsString);
  end;
end;

procedure Tf_RelatorioVendasComparativo.btnAtualizarClick(Sender: TObject);
begin
  ConfigurarDBGrid; // Atualizar cabeçalhos com as datas
  CarregarDados;
  CalcularTotaisRealizados; // Atualizar totais realizados
end;

procedure Tf_RelatorioVendasComparativo.btnFecharClick(Sender: TObject);
begin
  Close;
end;

procedure Tf_RelatorioVendasComparativo.btnExportarClick(Sender: TObject);
begin
  ExportarParaExcel;
end;

procedure Tf_RelatorioVendasComparativo.ExportarParaExcel;
var
  ExcelApp, ExcelWorkbook, ExcelWorksheet: Variant;
  Linha, Coluna: Integer;
  DataMes1, DataMes2, DataMes3: TDateTime;
  NomeMes1, NomeMes2, NomeMes3: string;
  NomeArquivo: string;
begin
  try
    // Verificar se há dados para exportar
    if cdsVendas.IsEmpty then
    begin
      ShowMessage('Não há dados para exportar!');
      Exit;
    end;

    // Calcular as datas dos três meses para os cabeçalhos
    DataMes3 := EncodeDate(FAnoBase, FMesBase, 1); // Mês atual
    DataMes2 := IncMonth(DataMes3, -1); // Mês anterior
    DataMes1 := IncMonth(DataMes3, -2); // Dois meses atrás

    // Formatar nomes dos meses para cabeçalhos
    NomeMes1 := FormatDateTime('mm/yyyy', DataMes1);
    NomeMes2 := FormatDateTime('mm/yyyy', DataMes2);
    NomeMes3 := FormatDateTime('mm/yyyy', DataMes3);

    // Criar aplicação Excel
    try
      ExcelApp := CreateOleObject('Excel.Application');
    except
      on E: Exception do
      begin
        ShowMessage('Erro ao abrir Excel. Verifique se o Microsoft Excel está instalado.' + #13#10 +
                   'Erro: ' + E.Message);
        Exit;
      end;
    end;

    try
      // Configurar Excel
      ExcelApp.Visible := False;
      ExcelApp.DisplayAlerts := False;

      // Criar nova planilha
      ExcelWorkbook := ExcelApp.Workbooks.Add;
      ExcelWorksheet := ExcelWorkbook.ActiveSheet;

      // Definir título da planilha
      ExcelWorksheet.Name := 'Vendas Comparativo';

      // Título principal
      ExcelWorksheet.Cells[1, 1] := 'RELATÓRIO COMPARATIVO DE VENDAS';
      ExcelWorksheet.Range['A1:J1'].Merge;
      ExcelWorksheet.Range['A1:J1'].Font.Bold := True;
      ExcelWorksheet.Range['A1:J1'].Font.Size := 14;
      ExcelWorksheet.Range['A1:J1'].HorizontalAlignment := -4108; // xlCenter

      // Período
      ExcelWorksheet.Cells[2, 1] := 'Período: ' + NomeMes1 + ' a ' + NomeMes3;
      ExcelWorksheet.Range['A2:J2'].Merge;
      ExcelWorksheet.Range['A2:J2'].Font.Bold := True;

      // Linha em branco
      Linha := 4;

      // Cabeçalhos das colunas
      ExcelWorksheet.Cells[Linha, 1] := 'Dia';

      // Primeiro mês
      ExcelWorksheet.Cells[Linha, 2] := NomeMes1;
      ExcelWorksheet.Cells[Linha + 1, 2] := 'Vendas';
      ExcelWorksheet.Cells[Linha + 1, 3] := 'Lucro';
      ExcelWorksheet.Cells[Linha + 1, 4] := 'Margem';

      // Segundo mês
      ExcelWorksheet.Cells[Linha, 5] := NomeMes2;
      ExcelWorksheet.Cells[Linha + 1, 5] := 'Vendas';
      ExcelWorksheet.Cells[Linha + 1, 6] := 'Lucro';
      ExcelWorksheet.Cells[Linha + 1, 7] := 'Margem';

      // Terceiro mês
      ExcelWorksheet.Cells[Linha, 8] := NomeMes3;
      ExcelWorksheet.Cells[Linha + 1, 8] := 'Vendas';
      ExcelWorksheet.Cells[Linha + 1, 9] := 'Lucro';
      ExcelWorksheet.Cells[Linha + 1, 10] := 'Margem';

      // Mesclar células dos cabeçalhos dos meses
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 2], ExcelWorksheet.Cells[Linha, 4]].Merge;
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 5], ExcelWorksheet.Cells[Linha, 7]].Merge;
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 8], ExcelWorksheet.Cells[Linha, 10]].Merge;

      // Formatar cabeçalhos
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 1], ExcelWorksheet.Cells[Linha + 1, 10]].Font.Bold := True;
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 1], ExcelWorksheet.Cells[Linha + 1, 10]].HorizontalAlignment := -4108; // xlCenter

      // Cor de fundo dos cabeçalhos (azul claro - RGB: 220, 230, 241)
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 1], ExcelWorksheet.Cells[Linha + 1, 10]].Interior.Color := RGB(220, 230, 241);

      // Mesclar célula do Dia
      ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 1], ExcelWorksheet.Cells[Linha + 1, 1]].Merge;

      Linha := Linha + 2;

      // Exportar dados com cores zebradas
      cdsVendas.First;
      while not cdsVendas.Eof do
      begin
        Coluna := 1;

        // Dia
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('Dia').AsInteger;
        Inc(Coluna);

        // Primeiro mês
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('VendasMes1').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('LucroMes1').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('MargemMes1').AsFloat;
        Inc(Coluna);

        // Segundo mês
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('VendasMes2').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('LucroMes2').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('MargemMes2').AsFloat;
        Inc(Coluna);

        // Terceiro mês
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('VendasMes3').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('LucroMes3').AsFloat;
        Inc(Coluna);
        ExcelWorksheet.Cells[Linha, Coluna] := cdsVendas.FieldByName('MargemMes3').AsFloat;

        // Aplicar cor zebrada (linhas alternadas)
        if (Linha mod 2) = 0 then
        begin
          // Linha par - cinza bem claro (RGB: 245, 245, 245)
          ExcelWorksheet.Range[ExcelWorksheet.Cells[Linha, 1], ExcelWorksheet.Cells[Linha, 10]].Interior.Color := RGB(245, 245, 245);
        end;
        // Linha ímpar fica branca (cor padrão)

        Inc(Linha);
        cdsVendas.Next;
      end;

      // Ajustar largura das colunas
      ExcelWorksheet.Columns.AutoFit;

      // Adicionar bordas
      ExcelWorksheet.Range[ExcelWorksheet.Cells[4, 1], ExcelWorksheet.Cells[Linha - 1, 10]].Borders.LineStyle := 1;

      // Definir nome do arquivo
      NomeArquivo := 'Vendas_Comparativo_' + FormatDateTime('yyyymmdd_hhnnss', Now) + '.xlsx';

      // Salvar arquivo
      ExcelApp.Visible := True;

      if MessageDlg('Deseja salvar o arquivo Excel?' + #13#10 +
                   'Nome: ' + NomeArquivo, mtConfirmation, [mbYes, mbNo], 0) = mrYes then
      begin
        try
          ExcelWorkbook.SaveAs(ExtractFilePath(Application.ExeName) + NomeArquivo);
          ShowMessage('Arquivo exportado com sucesso!' + #13#10 +
                     'Local: ' + ExtractFilePath(Application.ExeName) + NomeArquivo);
        except
          on E: Exception do
            ShowMessage('Erro ao salvar arquivo: ' + E.Message);
        end;
      end;

    except
      on E: Exception do
      begin
        ShowMessage('Erro durante a exportação: ' + E.Message);
        try
          if not VarIsEmpty(ExcelApp) then
          begin
            ExcelApp.DisplayAlerts := False;
            ExcelApp.Quit;
          end;
        except
          // Ignorar erros ao fechar Excel
        end;
      end;
    end;

  except
    on E: Exception do
      ShowMessage('Erro geral na exportação: ' + E.Message);
  end;
end;

end.
